import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ErrorService } from '../../services/errors/error.service';
import { ErrorMessages } from './error-messages';

@Component({
  selector: 'app-dialog-message',
  templateUrl: './dialog-message.component.html',
  styleUrls: ['./dialog-message.component.scss'],
})
export class DialogMessageComponent implements OnInit, OnDestroy {
  @ViewChild('dialogError') private dialogError?: TemplateRef<any>;
  public error: any;
  public showDetails = false;
  public sessionTimeout = 0;
  private ngUnsubscribe = new Subject<void>();
  private errorMessageDictionary = new ErrorMessages();
  showPopup = false;

  constructor(private errorService: ErrorService, public dialog: MatDialog) {
    this.initializeErrors();
  }

  ngOnInit(): void {
    setTimeout(() => {
      this.showPopup = true;
    }, 150);
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }

  /* Private Methods */
  private resetValues() {
    this.error = {};
    this.showDetails = false;
  }

  private initializeErrors() {
    this.errorService
      .getErrorsLegacy()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((error: any) => {
        this.resetValues();
        if (error.isCustomError === true) {
          this.error = error;
        } else {
          if (error.error && error.error.errorKey) {
            this.error = this.errorMessageDictionary.getMessageByKey(
              error.error.errorKey
            );
            if (!this.error) {
              this.error = this.errorMessageDictionary.getUnknownError(error); // if the error is not found, we have to add it into the dictionary
            } else if (error.error.errorDetails) {
              this.error.details = JSON.stringify(error.error.errorDetails);
            }
          } else {
            if (error.status >= 0) {
              this.error = this.errorMessageDictionary.getDefaultMessageByCode(
                error.status,
                JSON.stringify(error)
              );
            } else {
              this.error = this.errorMessageDictionary.getDefaultMessageByCode(
                -1,
                JSON.stringify(error)
              );
            }
          }
        }
        if (this.dialogError) {
          this.dialog.open(this.dialogError, {
            disableClose: true,
            panelClass: 'customErrorDialog',
          });
        }
      });
  }
}
