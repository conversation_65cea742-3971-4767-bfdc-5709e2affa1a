import { marker as _ } from '@biesbjerg/ngx-translate-extract-marker';

export class ErrorMessages {
  private defaultMessages: Array<DefaultMessage> = [
    {
      errorCode: -1,
      type: 'error',
      header: _('errors.unhandled.header'),
      content: _('errors.unhandled.content'),
      details: '',
    },
    {
      errorCode: 0,
      type: 'error',
      header: _('errors.0.header'),
      content: _('errors.0.content'),
      details: '',
    },
    {
      errorCode: 400,
      type: 'error',
      header: _('errors.400.default.header'),
      content: _('errors.400.default.content'),
      details: '',
    },
    {
      errorCode: 401,
      type: 'error',
      header: _('errors.401.default.header'),
      content: _('errors.401.default.content'),
      details: '',
    },
    {
      errorCode: 403,
      type: 'error',
      header: _('errors.403.default.header'),
      content: _('errors.403.default.content'),
      details: '',
    },
    {
      errorCode: 404,
      type: 'error',
      header: _('errors.404.default.header'),
      content: _('errors.404.default.content'),
      details: '',
    },
    {
      errorCode: 500,
      type: 'error',
      header: _('errors.500.default.header'),
      content: _('errors.500.default.content'),
      details: '',
    },
  ];

  private keyMessages: Array<KeyMessage> = [];

  public getMessageByKey(msgKey: string): KeyMessage | undefined {
    return this.keyMessages.find((msg) => msg.key === msgKey);
  }

  public getDefaultMessageByCode(
    errorCode: number,
    errorDetails: string
  ): DefaultMessage | undefined {
    const defaultMessage = this.defaultMessages.find(
      (msg) => msg.errorCode === errorCode
    );
    if (defaultMessage) {
      defaultMessage.details = errorDetails;
    }
    return defaultMessage;
  }

  public getUnknownError(error: any): KeyMessage {
    // Handle both legacy and enhanced error formats
    const errorKey = error.errorKey || error.error?.errorKey;
    const statusCode = error.statusCode || error.status;
    const message = error.message || error.error?.message;

    const unknownError = {
      key: errorKey,
      type: 'warn',
      header: `Error ${statusCode}`,
      content: message,
      details: `Add the error key "${errorKey}" to handle multilingualism.`,
    };
    console.warn(
      '[Missing error] - Add this error key to handle multilingualism into the error-messages.ts',
      unknownError
    );
    return unknownError;
  }
}

export interface KeyMessage {
  key: string;
  type: string;
  header: string;
  content: string;
  details?: string;
  action?: { type: string; name: string };
}

export interface DefaultMessage {
  errorCode: number;
  type: string;
  header: string;
  content: string;
  details: string;
}
