import { Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  map,
  filter as rxFilter,
  takeUntil,
} from 'rxjs/operators';
import { environment } from '../../../../environments/environment';

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Error categories for better organization
 */
export enum ErrorCategory {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  BUSINESS_LOGIC = 'business_logic',
  SYSTEM = 'system',
  USER_INPUT = 'user_input',
  EXTERNAL_SERVICE = 'external_service',
}

/**
 * Error context interface for additional metadata
 */
export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  userAgent?: string;
  url?: string;
  timestamp?: Date;
  component?: string;
  action?: string;
  additionalData?: Record<string, any>;
}

/**
 * Enhanced error interface with comprehensive metadata
 */
export interface EnhancedError {
  id: string;
  originalError: any;
  message: string;
  severity: ErrorSeverity;
  category: ErrorCategory;
  context: ErrorContext;
  isCustomError?: boolean;
  errorKey?: string;
  statusCode?: number;
  retryable?: boolean;
  retryCount?: number;
  maxRetries?: number;
  suppressDialog?: boolean;
  handled?: boolean;
  timestamp: Date;
}

/**
 * Error filter configuration
 */
export interface ErrorFilter {
  urlPatterns?: string[];
  statusCodes?: number[];
  errorKeys?: string[];
  categories?: ErrorCategory[];
  severities?: ErrorSeverity[];
  suppressDialog?: boolean;
  suppressLogging?: boolean;
}

/**
 * Error service configuration
 */
export interface ErrorServiceConfig {
  enableLogging?: boolean;
  enableAnalytics?: boolean;
  maxErrorHistory?: number;
  debounceTime?: number;
  defaultRetryAttempts?: number;
  logLevel?: 'error' | 'warn' | 'info' | 'debug';
}

/**
 * Error statistics for analytics
 */
export interface ErrorStatistics {
  totalErrors: number;
  errorsByCategory: Record<ErrorCategory, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  errorsByStatusCode: Record<number, number>;
  mostCommonErrors: Array<{ errorKey: string; count: number }>;
  errorTrends: Array<{ timestamp: Date; count: number }>;
}

@Injectable({
  providedIn: 'root',
})
export class ErrorService implements OnDestroy {
  private readonly destroy$ = new Subject<void>();
  private readonly errors$ = new Subject<EnhancedError>();
  private readonly errorHistory$ = new BehaviorSubject<EnhancedError[]>([]);
  private readonly statistics$ = new BehaviorSubject<ErrorStatistics>(
    this.initializeStatistics()
  );

  private requestBlackList: string[] = [];
  private errorFilters: ErrorFilter[] = [];
  private errorHistory: EnhancedError[] = [];
  private config: ErrorServiceConfig;

  constructor() {
    this.config = {
      enableLogging: !environment.production,
      enableAnalytics: true,
      maxErrorHistory: 100,
      debounceTime: 300,
      defaultRetryAttempts: 3,
      logLevel: environment.production ? 'error' : 'debug',
    };

    this.initializeErrorHandling();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.errors$.complete();
    this.errorHistory$.complete();
    this.statistics$.complete();
  }

  /**
   * Initialize error handling pipeline
   */
  private initializeErrorHandling(): void {
    this.errors$
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(this.config.debounceTime || 300),
        distinctUntilChanged(
          (prev, curr) =>
            prev.message === curr.message &&
            prev.statusCode === curr.statusCode &&
            Math.abs(curr.timestamp.getTime() - prev.timestamp.getTime()) < 5000
        )
      )
      .subscribe((error) => {
        this.processError(error);
      });
  }

  /**
   * Add error with enhanced metadata and processing
   */
  public addError(error: any, context?: Partial<ErrorContext>): string {
    const enhancedError = this.createEnhancedError(error, context);

    if (this.shouldSuppressError(enhancedError)) {
      return enhancedError.id;
    }

    this.errors$.next(enhancedError);
    return enhancedError.id;
  }

  /**
   * Add custom error with specific configuration
   */
  public addCustomError(
    message: string,
    category: ErrorCategory = ErrorCategory.SYSTEM,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    context?: Partial<ErrorContext>,
    options?: {
      errorKey?: string;
      retryable?: boolean;
      suppressDialog?: boolean;
    }
  ): string {
    const customError = {
      isCustomError: true,
      message,
      category,
      severity,
      error: {
        errorKey: options?.errorKey,
        message,
      },
      ...options,
    };

    return this.addError(customError, {
      ...context,
      component: context?.component || 'custom',
    });
  }

  /**
   * Get errors observable with optional filtering
   */
  public getErrors(
    errorFilter?: Partial<ErrorFilter>
  ): Observable<EnhancedError> {
    return this.errors$.pipe(
      rxFilter((error: EnhancedError) =>
        this.matchesFilter(error, errorFilter)
      ),
      takeUntil(this.destroy$)
    );
  }

  /**
   * Get errors observable (backward compatibility - returns legacy format)
   * @deprecated Use getErrors() instead for enhanced error handling
   */
  public getErrorsLegacy(): Observable<any> {
    return this.errors$.pipe(
      rxFilter((error: EnhancedError) => !error.suppressDialog),
      map((error: EnhancedError) => this.convertToLegacyFormat(error)),
      takeUntil(this.destroy$)
    );
  }

  /**
   * Get error history
   */
  public getErrorHistory(): Observable<EnhancedError[]> {
    return this.errorHistory$.asObservable();
  }

  /**
   * Get error statistics
   */
  public getStatistics(): Observable<ErrorStatistics> {
    return this.statistics$.asObservable();
  }

  /**
   * Register unhandled URL to the blacklist (backward compatibility)
   */
  public registerUnhandledRequestURL(url: string): void {
    this.requestBlackList.push(url);
  }

  /**
   * Check if request URL is blacklisted (backward compatibility)
   */
  public isRequestBlackListed(url: string): boolean {
    return this.requestBlackList.some((rq) => url.includes(rq));
  }

  /**
   * Add error filter
   */
  public addErrorFilter(filter: ErrorFilter): void {
    this.errorFilters.push(filter);
  }

  /**
   * Remove error filter
   */
  public removeErrorFilter(filter: ErrorFilter): void {
    const index = this.errorFilters.indexOf(filter);
    if (index > -1) {
      this.errorFilters.splice(index, 1);
    }
  }

  /**
   * Clear all error filters
   */
  public clearErrorFilters(): void {
    this.errorFilters = [];
  }

  /**
   * Update service configuration
   */
  public updateConfig(config: Partial<ErrorServiceConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Clear error history
   */
  public clearErrorHistory(): void {
    this.errorHistory = [];
    this.errorHistory$.next([]);
  }

  /**
   * Mark error as handled
   */
  public markErrorAsHandled(errorId: string): void {
    const error = this.errorHistory.find((e) => e.id === errorId);
    if (error) {
      error.handled = true;
      this.errorHistory$.next([...this.errorHistory]);
    }
  }

  /**
   * Retry failed operation
   */
  public retryError(
    errorId: string,
    retryFunction: () => Observable<any>
  ): Observable<any> {
    const error = this.errorHistory.find((e) => e.id === errorId);
    if (!error || !error.retryable) {
      throw new Error('Error is not retryable or not found');
    }

    error.retryCount = (error.retryCount || 0) + 1;

    if (
      error.retryCount >
      (error.maxRetries || this.config.defaultRetryAttempts || 3)
    ) {
      throw new Error('Maximum retry attempts exceeded');
    }

    return retryFunction();
  }

  /**
   * Initialize statistics object
   */
  private initializeStatistics(): ErrorStatistics {
    return {
      totalErrors: 0,
      errorsByCategory: {} as Record<ErrorCategory, number>,
      errorsBySeverity: {} as Record<ErrorSeverity, number>,
      errorsByStatusCode: {},
      mostCommonErrors: [],
      errorTrends: [],
    };
  }

  /**
   * Create enhanced error from raw error
   */
  private createEnhancedError(
    error: any,
    context?: Partial<ErrorContext>
  ): EnhancedError {
    const timestamp = new Date();
    const id = this.generateErrorId();

    // Extract error information
    const isHttpError = error?.status !== undefined;
    const isCustomError = error?.isCustomError === true;

    let message = '';
    let statusCode: number | undefined;
    let errorKey: string | undefined;
    let category = ErrorCategory.SYSTEM;
    let severity = ErrorSeverity.MEDIUM;

    if (isCustomError) {
      message = error.message || 'Custom error occurred';
      errorKey = error.error?.errorKey;
      category = this.determineCategory(error);
      severity = this.determineSeverity(error);
    } else if (isHttpError) {
      statusCode = error.status;
      message =
        error.error?.message || error.message || `HTTP ${statusCode} Error`;
      errorKey = error.error?.errorKey;
      category = this.categorizeHttpError(statusCode || 0);
      severity = this.determineSeverityFromStatus(statusCode || 0);
    } else {
      message = error?.message || 'Unknown error occurred';
      category = ErrorCategory.SYSTEM;
      severity = ErrorSeverity.HIGH;
    }

    const enhancedError: EnhancedError = {
      id,
      originalError: error,
      message,
      severity,
      category,
      context: {
        timestamp,
        url: window.location.href,
        userAgent: navigator.userAgent,
        ...context,
      },
      isCustomError,
      errorKey,
      statusCode,
      retryable: this.isRetryable(error, statusCode),
      retryCount: 0,
      maxRetries: this.config.defaultRetryAttempts,
      suppressDialog: error?.suppressDialog || false,
      handled: false,
      timestamp,
    };

    return enhancedError;
  }

  /**
   * Process error through the pipeline
   */
  private processError(error: EnhancedError): void {
    // Add to history
    this.addToHistory(error);

    // Update statistics
    this.updateStatistics(error);

    // Log error if enabled
    if (this.config.enableLogging && !this.shouldSuppressLogging(error)) {
      this.logError(error);
    }

    // Emit for dialog display (backward compatibility)
    if (!error.suppressDialog) {
      // Convert back to original format for backward compatibility
      const legacyError = this.convertToLegacyFormat(error);
      // TODO: The dialog component will need to be updated to handle EnhancedError
      // For now, we emit the legacy format to maintain compatibility
      console.debug('Legacy error format for dialog:', legacyError);
    }
  }

  /**
   * Check if error should be suppressed
   */
  private shouldSuppressError(error: EnhancedError): boolean {
    // Check URL blacklist
    if (error.context.url && this.isRequestBlackListed(error.context.url)) {
      return true;
    }

    // Check error filters
    return this.errorFilters.some((filter) =>
      this.matchesFilter(error, filter)
    );
  }

  /**
   * Check if error matches filter
   */
  private matchesFilter(
    error: EnhancedError,
    filter?: Partial<ErrorFilter>
  ): boolean {
    if (!filter) return true;

    if (
      filter.statusCodes &&
      error.statusCode &&
      !filter.statusCodes.includes(error.statusCode)
    ) {
      return false;
    }

    if (
      filter.errorKeys &&
      error.errorKey &&
      !filter.errorKeys.includes(error.errorKey)
    ) {
      return false;
    }

    if (filter.categories && !filter.categories.includes(error.category)) {
      return false;
    }

    if (filter.severities && !filter.severities.includes(error.severity)) {
      return false;
    }

    if (filter.urlPatterns && error.context.url) {
      const matchesUrl = filter.urlPatterns.some((pattern) =>
        error.context.url!.includes(pattern)
      );
      if (!matchesUrl) return false;
    }

    return true;
  }

  /**
   * Check if logging should be suppressed for this error
   */
  private shouldSuppressLogging(error: EnhancedError): boolean {
    return this.errorFilters.some(
      (filter) => filter.suppressLogging && this.matchesFilter(error, filter)
    );
  }

  /**
   * Add error to history with size management
   */
  private addToHistory(error: EnhancedError): void {
    this.errorHistory.unshift(error);

    // Maintain max history size
    if (this.errorHistory.length > (this.config.maxErrorHistory || 100)) {
      this.errorHistory = this.errorHistory.slice(
        0,
        this.config.maxErrorHistory || 100
      );
    }

    this.errorHistory$.next([...this.errorHistory]);
  }

  /**
   * Update error statistics
   */
  private updateStatistics(error: EnhancedError): void {
    const currentStats = this.statistics$.value;

    // Update total count
    currentStats.totalErrors++;

    // Update category count
    currentStats.errorsByCategory[error.category] =
      (currentStats.errorsByCategory[error.category] || 0) + 1;

    // Update severity count
    currentStats.errorsBySeverity[error.severity] =
      (currentStats.errorsBySeverity[error.severity] || 0) + 1;

    // Update status code count
    if (error.statusCode) {
      currentStats.errorsByStatusCode[error.statusCode] =
        (currentStats.errorsByStatusCode[error.statusCode] || 0) + 1;
    }

    // Update most common errors
    if (error.errorKey) {
      const existingError = currentStats.mostCommonErrors.find(
        (e) => e.errorKey === error.errorKey
      );
      if (existingError) {
        existingError.count++;
      } else {
        currentStats.mostCommonErrors.push({
          errorKey: error.errorKey,
          count: 1,
        });
      }

      // Sort and keep top 10
      currentStats.mostCommonErrors.sort((a, b) => b.count - a.count);
      currentStats.mostCommonErrors = currentStats.mostCommonErrors.slice(
        0,
        10
      );
    }

    // Update trends (hourly buckets)
    const hourBucket = new Date(error.timestamp);
    hourBucket.setMinutes(0, 0, 0);

    const existingTrend = currentStats.errorTrends.find(
      (t) => t.timestamp.getTime() === hourBucket.getTime()
    );

    if (existingTrend) {
      existingTrend.count++;
    } else {
      currentStats.errorTrends.push({ timestamp: hourBucket, count: 1 });
    }

    // Keep only last 24 hours
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    currentStats.errorTrends = currentStats.errorTrends.filter(
      (t) => t.timestamp >= oneDayAgo
    );

    this.statistics$.next(currentStats);
  }

  /**
   * Log error based on configuration
   */
  private logError(error: EnhancedError): void {
    const logLevel = this.config.logLevel || 'error';
    const logMessage = `[${error.severity.toUpperCase()}] ${error.category}: ${
      error.message
    }`;
    const logData = {
      id: error.id,
      category: error.category,
      severity: error.severity,
      statusCode: error.statusCode,
      errorKey: error.errorKey,
      context: error.context,
      originalError: error.originalError,
    };

    switch (logLevel) {
      case 'debug':
        console.debug(logMessage, logData);
        break;
      case 'info':
        if (error.severity === ErrorSeverity.LOW) {
          console.info(logMessage, logData);
        } else {
          console.error(logMessage, logData);
        }
        break;
      case 'warn':
        if (
          error.severity === ErrorSeverity.LOW ||
          error.severity === ErrorSeverity.MEDIUM
        ) {
          console.warn(logMessage, logData);
        } else {
          console.error(logMessage, logData);
        }
        break;
      case 'error':
      default:
        console.error(logMessage, logData);
        break;
    }
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Determine error category from custom error
   */
  private determineCategory(error: any): ErrorCategory {
    const errorKey = error.error?.errorKey?.toLowerCase() || '';

    if (
      errorKey.includes('auth') ||
      errorKey.includes('login') ||
      errorKey.includes('token')
    ) {
      return ErrorCategory.AUTHENTICATION;
    }
    if (
      errorKey.includes('permission') ||
      errorKey.includes('access') ||
      errorKey.includes('forbidden')
    ) {
      return ErrorCategory.AUTHORIZATION;
    }
    if (
      errorKey.includes('validation') ||
      errorKey.includes('invalid') ||
      errorKey.includes('required')
    ) {
      return ErrorCategory.VALIDATION;
    }
    if (
      errorKey.includes('business') ||
      errorKey.includes('rule') ||
      errorKey.includes('logic')
    ) {
      return ErrorCategory.BUSINESS_LOGIC;
    }
    if (errorKey.includes('input') || errorKey.includes('user')) {
      return ErrorCategory.USER_INPUT;
    }
    if (
      errorKey.includes('external') ||
      errorKey.includes('service') ||
      errorKey.includes('api')
    ) {
      return ErrorCategory.EXTERNAL_SERVICE;
    }

    return ErrorCategory.SYSTEM;
  }

  /**
   * Determine error severity from custom error
   */
  private determineSeverity(error: any): ErrorSeverity {
    const errorKey = error.error?.errorKey?.toLowerCase() || '';

    if (errorKey.includes('critical') || errorKey.includes('fatal')) {
      return ErrorSeverity.CRITICAL;
    }
    if (errorKey.includes('high') || errorKey.includes('severe')) {
      return ErrorSeverity.HIGH;
    }
    if (errorKey.includes('low') || errorKey.includes('minor')) {
      return ErrorSeverity.LOW;
    }

    return ErrorSeverity.MEDIUM;
  }

  /**
   * Categorize HTTP error by status code
   */
  private categorizeHttpError(statusCode: number): ErrorCategory {
    if (statusCode === 401) {
      return ErrorCategory.AUTHENTICATION;
    }
    if (statusCode === 403) {
      return ErrorCategory.AUTHORIZATION;
    }
    if (statusCode >= 400 && statusCode < 500) {
      return ErrorCategory.VALIDATION;
    }
    if (statusCode >= 500) {
      return ErrorCategory.SYSTEM;
    }
    if (statusCode === 0 || statusCode < 0) {
      return ErrorCategory.NETWORK;
    }

    return ErrorCategory.SYSTEM;
  }

  /**
   * Determine severity from HTTP status code
   */
  private determineSeverityFromStatus(statusCode: number): ErrorSeverity {
    if (statusCode >= 500) {
      return ErrorSeverity.HIGH;
    }
    if (statusCode === 401 || statusCode === 403) {
      return ErrorSeverity.MEDIUM;
    }
    if (statusCode === 404) {
      return ErrorSeverity.LOW;
    }
    if (statusCode >= 400 && statusCode < 500) {
      return ErrorSeverity.MEDIUM;
    }
    if (statusCode === 0 || statusCode < 0) {
      return ErrorSeverity.HIGH;
    }

    return ErrorSeverity.MEDIUM;
  }

  /**
   * Check if error is retryable
   */
  private isRetryable(_error: any, statusCode?: number): boolean {
    // Network errors are usually retryable
    if (statusCode === 0 || statusCode === undefined) {
      return true;
    }

    // Server errors are retryable
    if (statusCode >= 500) {
      return true;
    }

    // Timeout errors are retryable
    if (statusCode === 408 || statusCode === 504) {
      return true;
    }

    // Rate limiting is retryable
    if (statusCode === 429) {
      return true;
    }

    // Client errors are generally not retryable
    if (statusCode >= 400 && statusCode < 500) {
      return false;
    }

    return false;
  }

  /**
   * Convert enhanced error back to legacy format for backward compatibility
   */
  private convertToLegacyFormat(error: EnhancedError): any {
    return {
      ...error.originalError,
      status: error.statusCode,
      error: {
        errorKey: error.errorKey,
        message: error.message,
        errorDetails: error.context.additionalData,
      },
      isCustomError: error.isCustomError,
    };
  }
}
